import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

class TestJsonWriting {
    
    static class TestProfileData {
        String profileName;
        String sourceType;
        String sourcePath;
        String sheetName;
        String targetHost;
        String targetUsername;
        String targetPath;
        String status;
        Integer componentsCreated;
        String lastError;
        String lastSyncTime;
    }
    
    public static void main(String[] args) {
        try {
            System.out.println("Testing JSON writing...");
            
            // Create test data
            TestProfileData data = new TestProfileData();
            data.profileName = "Test Profile";
            data.sourceType = "Excel";
            data.sourcePath = "C:\\Test\\test.xlsx";
            data.sheetName = "TestSheet";
            data.targetHost = "localhost";
            data.targetUsername = "admin";
            data.targetPath = "station:|slot:/Test";
            data.status = "Test";
            data.componentsCreated = 42;
            data.lastError = "";
            data.lastSyncTime = "";
            
            // Create Gson instance
            Gson gson = new GsonBuilder()
                .setPrettyPrinting()
                .setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ")
                .create();
            
            // Test directory
            File testDir = new File("C:\\Users\\<USER>\\Niagara4.13\\OptimizerSupervisor\\shared\\N4DataSync\\profiles");
            if (!testDir.exists()) {
                testDir.mkdirs();
            }
            
            // Write to file
            File testFile = new File(testDir, "test-json-output.json");
            try (FileWriter writer = new FileWriter(testFile)) {
                gson.toJson(data, writer);
            }
            
            System.out.println("JSON file created: " + testFile.getAbsolutePath());
            System.out.println("File exists: " + testFile.exists());
            System.out.println("File size: " + testFile.length() + " bytes");
            
            // Read it back to verify
            if (testFile.exists() && testFile.length() > 0) {
                System.out.println("JSON writing test: SUCCESS");
            } else {
                System.out.println("JSON writing test: FAILED");
            }
            
        } catch (IOException e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

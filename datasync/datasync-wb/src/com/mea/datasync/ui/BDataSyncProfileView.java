// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.ui.BLabel;
import javax.baja.ui.BWidget;
import javax.baja.ui.HyperlinkInfo;
import javax.baja.ui.event.BMouseEvent;
import javax.baja.ui.pane.BBorderPane;
import javax.baja.ui.pane.BEdgePane;
import javax.baja.workbench.BWbShell;
import javax.baja.workbench.mgr.BAbstractManager;
import javax.baja.workbench.mgr.BMgrTable;
import javax.baja.workbench.mgr.MgrColumn;
import javax.baja.workbench.mgr.MgrController;
import javax.baja.workbench.mgr.MgrModel;
import javax.baja.workbench.mgr.MgrState;
import javax.baja.workbench.mgr.MgrTypeInfo;

import com.mea.datasync.model.BConnectionProfile;
import com.mea.datasync.persistence.ProfileManager;

/**
 * BDataSyncProfileView provides a manager-style view for the DataSync tool
 * that displays and manages connection profiles in a table format.
 * This follows the same pattern as BDeviceManager, BDriverManager, and BPointManager.
 */
@NiagaraType(
  agent = @AgentOn(
    types = { "datasync:DataSyncTool" }
  )
)
public class BDataSyncProfileView extends BAbstractManager {

  static {
    System.out.println("BDataSyncProfileView class loaded!");
  }

  // ProfileManager instance for persistence
  private ProfileManager profileManager;

//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.ui.BDataSyncProfileView(184651739)1.0$ @*/
/* Generated Mon Jun 23 01:46:59 AEST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDataSyncProfileView.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// BAbstractManager Implementation
////////////////////////////////////////////////////////////////

  @Override
  protected MgrModel makeModel() { return new DataSyncModel(this); }

  @Override
  protected MgrController makeController() { return new DataSyncController(this); }

  @Override
  protected MgrState makeState() { return new DataSyncState(); }

  /**
   * Override to provide a custom title for the manager view.
   */
  public String getDisplayName() {
    return "DataSync Connection Profiles";
  }

  /**
   * Initialize the ProfileManager.
   */
  protected void initializeProfileManager() {
    // Initialize ProfileManager
    if (profileManager == null) {
      profileManager = new ProfileManager();
    }
  }

  /**
   * Save a profile to persistent storage.
   */
  public void saveProfileToStorage(BConnectionProfile profile) {
    if (profileManager != null && profile != null) {
      try {
        String profileName = profile.getSlotPath().toString();
        // Extract just the slot name from the full path
        if (profileName.contains(":")) {
          profileName = profileName.substring(profileName.lastIndexOf(":") + 1);
        }
        boolean saved = profileManager.saveProfile(profile, profileName);
        if (saved) {
          System.out.println("Profile saved to storage: " + profileName);
        } else {
          System.err.println("Failed to save profile: " + profileName);
        }
      } catch (Exception e) {
        System.err.println("Error saving profile: " + e.getMessage());
        e.printStackTrace();
      }
    }
  }

  /**
   * Save all current profiles to storage.
   * This can be called manually to persist all profiles.
   */
  public void saveAllProfilesToStorage() {
    initializeProfileManager();
    BComponent target = getTarget();
    if (target != null) {
      BComponent[] children = target.getChildren(BConnectionProfile.class);
      System.out.println("Saving " + children.length + " profiles to storage");
      for (BComponent child : children) {
        if (child instanceof BConnectionProfile) {
          saveProfileToStorage((BConnectionProfile) child);
        }
      }
    }
  }

  /**
   * Force save all profiles immediately (for testing).
   * This method can be called from the console or debug tools.
   */
  public void forceSaveAllProfiles() {
    System.out.println("Force saving all profiles...");
    saveAllProfilesToStorage();

    // Also test the ProfileManager directly
    if (profileManager != null) {
      profileManager.testSaveProfile();
    }
  }

////////////////////////////////////////////////////////////////
// Model
////////////////////////////////////////////////////////////////

  /**
   * DataSyncModel manages the logical model of connection profiles
   * in the DataSync tool. It defines the table columns and data display.
   */
  class DataSyncModel extends MgrModel {

    DataSyncModel(BDataSyncProfileView manager) {
      super(manager);
    }

    protected String makeTableTitle() {
      return "DataSync Connection Profiles";
    }

    protected MgrColumn[] makeColumns() {
      return new MgrColumn[] {
        new MgrColumn.Name(),
        new MgrColumn.Prop(BConnectionProfile.sourceType),
        new MgrColumn.Prop(BConnectionProfile.sourcePath),
        new MgrColumn.Prop(BConnectionProfile.sheetName),
        new MgrColumn.Prop(BConnectionProfile.targetHost),
        new MgrColumn.Prop(BConnectionProfile.targetPath),
        new MgrColumn.Prop(BConnectionProfile.status),
        new MgrColumn.Prop(BConnectionProfile.lastSync),
        new MgrColumn.Prop(BConnectionProfile.componentsCreated),
      };
    }

    public Type[] getIncludeTypes() {
      return new Type[] { BConnectionProfile.TYPE };
    }

    public MgrTypeInfo[] getNewTypes() {
      return MgrTypeInfo.makeArray(BConnectionProfile.TYPE);
    }

    /**
     * Get the base type supported by the new operation.
     */
    public Type getBaseNewType() {
      return BConnectionProfile.TYPE;
    }

    /**
     * Override to load connection profiles from persistent storage.
     */
    public void load(BComponent target) {
      super.load(target);

      // Load profiles from persistent storage
      loadProfilesFromStorage(target);

      // Add a listener to save profiles when they're modified
      setupAutoSave(target);
    }

    /**
     * Set up automatic saving when components are added or modified.
     */
    private void setupAutoSave(BComponent target) {
      try {
        // Add a simple timer-based auto-save mechanism
        // This will periodically save all profiles
        java.util.Timer autoSaveTimer = new java.util.Timer("ProfileAutoSave", true);
        autoSaveTimer.scheduleAtFixedRate(new java.util.TimerTask() {
          @Override
          public void run() {
            try {
              BDataSyncProfileView view = (BDataSyncProfileView) getManager();
              view.saveAllProfilesToStorage();
            } catch (Exception e) {
              System.err.println("Auto-save error: " + e.getMessage());
            }
          }
        }, 10000, 30000); // Save every 30 seconds after initial 10 second delay

        System.out.println("Auto-save timer started");

      } catch (Exception e) {
        System.err.println("Error setting up auto-save: " + e.getMessage());
      }
    }

    /**
     * Load connection profiles from the target component.
     * In Niagara, manager views work with components that are children of the target.
     */
    private void loadProfilesFromTarget(BComponent target) {
      try {
        System.out.println("DataSyncProfileView: Loading profiles from target component");
        System.out.println("Target component: " + target.getSlotPath());

        // Get existing profile children
        BComponent[] existingProfiles = target.getChildren(BConnectionProfile.class);
        System.out.println("Found " + existingProfiles.length + " existing profiles in target");

        // If no profiles exist, create some sample ones for demonstration
        if (existingProfiles.length == 0) {
          System.out.println("No profiles found, creating initial sample profiles");
          createInitialSampleProfiles(target);
        }

        // List all profiles for debugging
        BComponent[] allProfiles = target.getChildren(BConnectionProfile.class);
        System.out.println("Total profiles after initialization: " + allProfiles.length);
        for (BComponent profile : allProfiles) {
          System.out.println("  Profile: " + profile.getSlotPath().getSlotName());
        }

      } catch (Exception e) {
        System.err.println("Error loading profiles from target: " + e.getMessage());
        e.printStackTrace();
      }
    }

    /**
     * Create initial sample profiles for first-time users.
     */
    private void createInitialSampleProfiles(ProfileManager profileManager) {
      try {
        // Sample Profile 1 - Building A HVAC
        BConnectionProfile profile1 = new BConnectionProfile();
        profile1.setSourceType("Excel");
        profile1.setSourcePath("C:\\Data\\BuildingA_HVAC.xlsx");
        profile1.setSheetName("Equipment");
        profile1.setTargetHost("*************");
        profile1.setTargetPath("station:|slot:/Drivers");
        profile1.setStatus("Success");
        profile1.setComponentsCreated(45);
        boolean saved1 = profileManager.saveProfile(profile1, "Building A HVAC");
        System.out.println("Sample profile 1 saved: " + saved1);

        // Sample Profile 2 - Building B Lighting
        BConnectionProfile profile2 = new BConnectionProfile();
        profile2.setSourceType("Excel");
        profile2.setSourcePath("C:\\Data\\BuildingB_Lighting.xlsx");
        profile2.setSheetName("Points");
        profile2.setTargetHost("*************");
        profile2.setTargetPath("station:|slot:/Drivers/Lighting");
        profile2.setStatus("Error");
        profile2.setComponentsCreated(23);
        boolean saved2 = profileManager.saveProfile(profile2, "Building B Lighting");
        System.out.println("Sample profile 2 saved: " + saved2);

        // Sample Profile 3 - Chiller Plant
        BConnectionProfile profile3 = new BConnectionProfile();
        profile3.setSourceType("Excel");
        profile3.setSourcePath("C:\\Data\\ChillerPlant.xlsx");
        profile3.setSheetName("Chillers");
        profile3.setTargetHost("*************");
        profile3.setTargetPath("station:|slot:/Drivers/HVAC");
        profile3.setStatus("Never Synced");
        profile3.setComponentsCreated(0);
        boolean saved3 = profileManager.saveProfile(profile3, "Chiller Plant");
        System.out.println("Sample profile 3 saved: " + saved3);

        System.out.println("Created initial sample profiles");

      } catch (Exception e) {
        System.err.println("Error creating initial sample profiles: " + e.getMessage());
        e.printStackTrace();
      }
    }

    /**
     * Sanitize a profile name for use as a component name.
     */
    private String sanitizeComponentName(String name) {
      if (name == null) return "unnamed";
      return name.replaceAll("[^a-zA-Z0-9_]", "_");
    }
  }

////////////////////////////////////////////////////////////////
// Controller
////////////////////////////////////////////////////////////////

  /**
   * DataSyncController handles user interactions with the connection profiles table.
   */
  class DataSyncController extends MgrController {

    DataSyncController(BDataSyncProfileView manager) {
      super(manager);
    }

    public void cellDoubleClicked(BMgrTable table, BMouseEvent event, int row, int col) {
      BComponent comp = table.getComponentAt(row);
      BWbShell shell = getWbShell();
      if (comp != null && shell != null)
        shell.hyperlink(new HyperlinkInfo(comp.getNavOrd(), event));
    }

    /**
     * Helper method to save all profiles to storage.
     * This can be called manually when needed.
     */
    public void saveAllProfilesToStorage() {
      BDataSyncProfileView view = (BDataSyncProfileView) getManager();
      BComponent target = view.getTarget();
      if (target != null) {
        BComponent[] children = target.getChildren(BConnectionProfile.class);
        for (BComponent child : children) {
          if (child instanceof BConnectionProfile) {
            view.saveProfileToStorage((BConnectionProfile) child);
          }
        }
      }
    }
  }

////////////////////////////////////////////////////////////////
// State
////////////////////////////////////////////////////////////////

  /**
   * DataSyncState manages view state and preferences for the DataSync manager.
   */
  class DataSyncState extends MgrState {

    /**
     * Override to provide custom state management for DataSync profiles.
     * This could include view preferences, column widths, sort orders, etc.
     */
    // Default implementation is sufficient for now
    // State management methods are inherited from MgrState
  }
}

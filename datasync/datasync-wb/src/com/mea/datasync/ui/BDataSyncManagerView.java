// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.ui.BLabel;
import javax.baja.ui.pane.BScrollPane;
import javax.baja.ui.pane.BEdgePane;
import javax.baja.workbench.view.BWbView;

/**
 * BDataSyncManagerView is a simple view for the N4-DataSync tool.
 * It displays the DataSync table in a basic layout.
 */
@NiagaraType
public class BDataSyncManagerView extends BWbView {

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////

    public static final Type TYPE = Sys.loadType(BDataSyncManagerView.class);
    
    @Override
    public Type getType() { 
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.ui.BDataSyncManagerView(2979906276)1.0$ @*/
/* Generated Mon Jun 23 01:46:59 AEST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDataSyncManagerView.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
        return TYPE; 
    }

////////////////////////////////////////////////////////////////
// Constructor
////////////////////////////////////////////////////////////////

    public BDataSyncManagerView() {
        initializeComponents();
        layoutComponents();
    }

////////////////////////////////////////////////////////////////
// Component Initialization
////////////////////////////////////////////////////////////////

    private void initializeComponents() {
        // Create main table
        profileTable = new BDataSyncTable();
        
        // Create title label
        titleLabel = new BLabel("N4-DataSync Connection Profiles");
    }

    private void layoutComponents() {
        // Use BEdgePane for layout
        BEdgePane edgePane = new BEdgePane();
        
        // Title at top
        edgePane.setTop(titleLabel);
        
        // Main table in center with scroll pane
        BScrollPane scrollPane = new BScrollPane();
        scrollPane.setContent(profileTable);
        edgePane.setCenter(scrollPane);
        
        setContent(edgePane);
    }

////////////////////////////////////////////////////////////////
// Attributes
////////////////////////////////////////////////////////////////

    private BDataSyncTable profileTable;
    private BLabel titleLabel;
}
